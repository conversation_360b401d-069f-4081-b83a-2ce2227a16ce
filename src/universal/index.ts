import { type OrgTree } from './types'

export function flattenTreeToArray(nodes: OrgTree[]) {
  const result: {
    id: string
    name: string
    parent_id: string
    type: string
    level: number
  }[] = []
  function traverse(currentNodes: OrgTree[]) {
    for (const node of currentNodes) {
      result.push({
        id: node.id,
        name: node.name,
        parent_id: node.parent_id,
        type: node.type,
        level: node.level,
      })
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    }
  }
  traverse(nodes)
  return result
}
