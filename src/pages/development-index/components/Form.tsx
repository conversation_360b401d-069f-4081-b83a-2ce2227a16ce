import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate, useParams, useSearch } from '@tanstack/react-router'
import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Select,
  Spin,
} from 'antd'
import dayjs from 'dayjs'
import { useState, useEffect } from 'react'

import FormTip from '@/components/FormItemTip'
import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request.ts'

import type { DevelopmentIndex } from './types'

export function DevelopmentIndexForm({
  type = 'CREATE',
}: Readonly<{ type?: 'CREATE' | 'UPDATE' | 'VIEW' }>) {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { id: approvalNodeId } = useParams({ strict: false })
  const { consolidation } = useSearch({ strict: false })
  const [preQuarterData, setPreQuarterData] = useState<DevelopmentIndex>()
  const [editId, setEditId] = useState<string>()
  const [form] = Form.useForm()

  // 获取详情
  const { isLoading: isLoadingDetail } = useQuery({
    queryKey: [
      '/industry-new/development-index/detail-by-approval-node-id',
      approvalNodeId,
    ],
    queryFn: async ({ queryKey: [url, id] }) => {
      const res = await request<APIResponse<DevelopmentIndex>>(url as string, {
        method: 'GET',
        params: {
          approval_node_id: id,
        },
      })

      if (res.code === 200001) {
        const { period, ...data } = res.data

        const formData = {
          ...data,
          period: dayjs(period),
        }

        setEditId(data.id)
        form.setFieldsValue(formData)
        return formData
      }
      message.error(res.message)
    },
    enabled: !!approvalNodeId,
  })

  const onSubmit = useMutation({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mutationFn: async (values: Record<string, any>) => {
      const { period, ...data } = values
      const bodyData = {
        ...data,
        investment_year: dayjs(period).format('YYYY'),
        period: dayjs(period).format('YYYY-MM-01 HH:mm:ss'),
        consolidation: type === 'CREATE' ? 2 : data.consolidation, //创建默认传2
      }

      let res

      if (type === 'UPDATE') {
        res = await request<APIResponse<Record<string, string>>>(
          '/industry-new/development-index',
          {
            method: 'PUT',
            body: { id: editId, ...bodyData },
          },
        )
      } else {
        res = await request<APIResponse<Record<string, string>>>(
          '/industry-new/development-index',
          { method: 'POST', body: bodyData },
        )
      }

      if (res.code === 200001) {
        message.success('操作成功')
        await navigate({ to: '/strategic-report/development-index' })
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  // 填报周期
  const watchPreiod = Form.useWatch('period', form)

  const { isLoading } = useQuery({
    queryKey: [
      '/industry-new/development-index/save-init',
      watchPreiod,
      user?.company_id,
    ],
    queryFn: async ({ queryKey: [url, watchPreiod, company_id] }) => {
      const res = await request<
        APIResponse<{
          cur_quarter: DevelopmentIndex
          pre_quarter: DevelopmentIndex
        }>
      >(url, {
        method: 'GET',
        params: {
          company_id,
          period: dayjs(watchPreiod).format('YYYY-MM-01 HH:mm:ss'),
          edit: type === 'CREATE' ? 1 : 2,
        },
      })

      if (res.code === 200001) {
        const { cur_quarter, pre_quarter } = res.data
        setPreQuarterData(pre_quarter)
        form.setFieldsValue({
          fixed_asset_investment_amount:
            cur_quarter?.fixed_asset_investment_amount,
          equity_investment_amount: cur_quarter?.equity_investment_amount,
        })
        return cur_quarter
      }
      message.error(res.message)
    },
    enabled: !!watchPreiod && !!user?.company_id,
  })

  useEffect(() => {
    // 每次校验有值的数据
    if (preQuarterData) {
      const fields = form.getFieldsValue()
      // 将fields中有值的key筛选出来
      const keys = Object.keys(fields).filter(
        (key) => fields[key] || fields[key] === 0,
      )
      form.validateFields(keys)
    }
  }, [preQuarterData, form])

  //通用校验
  const commonValidate = (value: number, preValue: number | undefined) => {
    if (value < (preValue || 0)) {
      return Promise.reject('较上季减少')
    } else {
      return Promise.resolve()
    }
  }

  return (
    <Spin spinning={onSubmit.isPending || isLoading || isLoadingDetail}>
      <Form
        form={form}
        disabled={
          type === 'VIEW' || (type === 'UPDATE' && consolidation === '1')
        }
        labelCol={{ flex: '140px' }}
        labelWrap
        labelAlign="left"
        scrollToFirstError={{
          block: 'center',
          behavior: 'smooth',
        }}
        onFinish={onSubmit.mutate}
      >
        <div className="flex flex-col">
          <h4 className="mt-2 mb-6 text-sm font-semibold text-[#266EFF]">
            企业信息
          </h4>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              label="填报周期"
              name="period"
              rules={[{ required: true, message: '请选择填报周期' }]}
            >
              <DatePicker
                picker="quarter"
                className="w-full"
                placeholder="请选择"
              />
            </Form.Item>
            <Form.Item
              label="企业编号"
              name="company_code"
              rules={[{ required: true, message: '请输入企业编号' }]}
              initialValue={'080'}
            >
              <Input className="w-full" placeholder="请输入企业编号" disabled />
            </Form.Item>
          </div>
          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              label="企业名称"
              name="company_id"
              rules={[{ required: true, message: '请选择企业名称' }]}
            >
              <Select
                className="w-full"
                placeholder="请选择"
                options={
                  type === 'UPDATE'
                    ? [
                        {
                          label: form.getFieldValue('company_name'),
                          value: form.getFieldValue('company_id'),
                        },
                      ]
                    : [{ label: user?.company, value: user?.company_id }]
                }
                onChange={(_, option) => {
                  const value = option as { label: string; value: string }
                  form.setFieldValue('company_name', value?.label)
                }}
              />
            </Form.Item>
            <Form.Item name="company_name" noStyle></Form.Item>
          </div>

          <h4 className="mt-2 mb-6 text-sm font-semibold text-[#266EFF]">
            产业布局
          </h4>
          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="固定资产投资金额">
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="fixed_asset_investment_amount"
                  rules={[
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.fixed_asset_investment_amount,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="自动抓取"
                    suffix="万元"
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    controls={false}
                    disabled={true}
                  />
                </Form.Item>
                <FormTip title="本季度落地的固定资产投资金额。请与您公司在投资系统中填报的数据口径保持一致，需注意本指标应为本季度新增值（投资系统中填报的为累计值）" />
              </div>
            </Form.Item>
            <Form.Item label="股权投资金额">
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="equity_investment_amount"
                  rules={[
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.equity_investment_amount,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="自动抓取"
                    suffix="万元"
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    controls={false}
                    disabled={true}
                  />
                </Form.Item>
                <FormTip title="本季度落地的股权投资金额。请与您公司在投资系统中填报的数据口径保持一致，需注意本指标应为本季度新增值（投资系统中填报的为累计值）" />
              </div>
            </Form.Item>
          </div>
          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="上市企业数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="listed_company_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入上市企业数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.listed_company_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="个"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前的上市企业数量" />
              </div>
            </Form.Item>
            <Form.Item label="行业领先企业数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="industry_leading_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入行业领先企业数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.industry_leading_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="个"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前的高新技术企业、创新型中小企业、专精特新企业、单项冠军企业/产品、隐形冠军企业数量" />
              </div>
            </Form.Item>
          </div>

          <h4 className="mt-2 mb-6 text-sm font-semibold text-[#266EFF]">
            科技创新
          </h4>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="研发投入金额" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="rnd_investment_mount"
                  rules={[
                    {
                      required: true,
                      message: '请输入研发投入金额',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.rnd_investment_mount,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="万元"
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="本季度落地的研发投入金额" />
              </div>
            </Form.Item>
            <Form.Item label="参与建设科技研发、科技成果转化平台数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="tech_achievement_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入参与建设科技研发、科技成果转化平台数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.tech_achievement_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="个"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前参与建设的全国重点实验室、国防科技重点实验室、国家技术创新中心、新型研发机构、创新联合体、中试验证平台等平台数量" />
              </div>
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="累积获授权专利数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="patent_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入累积获授权专利数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(value, preQuarterData?.patent_count),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="个"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前已获得专利局授权的专利数量（全球范围内，含未下证的专利，授权专利最终会转化为专利权）" />
              </div>
            </Form.Item>
            <Form.Item label="参与制定国家级以上标准数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="national_standard_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入参与制定国家级以上标准数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.national_standard_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="个"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前累计参与制定的国家级及以上的标准数量（全球范围内）" />
              </div>
            </Form.Item>
          </div>

          <h4 className="mt-2 mb-6 text-sm font-semibold text-[#266EFF]">
            人才队伍
          </h4>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="科技领军人才数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="tech_leader_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入科技领军人才数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.tech_leader_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="人"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前在岗的科技领军人才数量，科技领军人才包括：1.在科技创新、关键核心技术攻关中取得重大突破、解决重大难题、作出重大贡献的人才。如中国科学院院士、中国工程院院士，国家最高科学技术奖获得者，其他国家科学院院士或工程院院士，世界一流高校、科研机构著名学者等。2.在业务板块涉及领域突破关键核心技术、作出较大贡献人才。如近五年国家科技成果奖励主要完成人，国家重点科研计划项目负责人，国家级重点学科、重点实验室、工程技术研究中心学术、技术带头人，国内外知名大学、研究机构教授等" />
              </div>
            </Form.Item>
            <Form.Item label="技能领军人才数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="skilled_leader_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入技能领军人才数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.skilled_leader_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="人"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前在岗的技能领军人才数量，技能领军人才包括：1.获中华技能大奖、全国技术能手、国家技能人才培育突出贡献个人等表彰的全国高技能人才；2.获全国劳动模范、五一劳动奖章、三八红旗手、青年五四奖章、青年岗位能手、巾帼建功标兵等国家级荣誉表彰的高技能人才；3.享受省级以上政府特殊津贴，或各省（自治区、直辖市）政府认定的“高精尖缺”高技能人才；4.获其他国家技能竞赛奖项的高技能人才" />
              </div>
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="研发人员数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="rnd_staff_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入研发人员数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(value, preQuarterData?.rnd_staff_count),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="人"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前在岗的研发人员数量" />
              </div>
            </Form.Item>
            <Form.Item label="员工总数" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="total_employee_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入员工总数',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.total_employee_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="人"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前在岗的员工总数" />
              </div>
            </Form.Item>
          </div>

          <h4 className="mt-2 mb-6 text-sm font-semibold text-[#266EFF]">
            产业生态
          </h4>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="合作客户数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="partner_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入合作客户数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(value, preQuarterData?.partner_count),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="个"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前的合作客户数量，包括服务客户（甲方）和合作供应商（乙方）" />
              </div>
            </Form.Item>
            <Form.Item label="参与的产业集群数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="industrial_cluster_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入参与的产业集群数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.industrial_cluster_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="个"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前参与的产业集群数量（含国家级、省部级）" />
              </div>
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="参与的产业联盟数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="industrial_alliance_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入参与的产业联盟数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.industrial_alliance_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="个"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至目前参与的产业联盟数量" />
              </div>
            </Form.Item>
            <Form.Item label="组织的产业交流活动数量" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="industry_activity_count"
                  rules={[
                    {
                      required: true,
                      message: '请输入组织的产业交流活动数量',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(
                          value,
                          preQuarterData?.industry_activity_count,
                        ),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="个"
                    style={{ width: '100%' }}
                    min={0}
                    precision={0}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="本季度主办、承办、协办的产业交流、沙龙研讨等活动数量" />
              </div>
            </Form.Item>
          </div>

          <h4 className="mt-2 mb-6 text-sm font-semibold text-[#266EFF]">
            发展成效
          </h4>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="资产总额" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="asset_total"
                  rules={[
                    {
                      required: true,
                      message: '请输入资产总额',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(value, preQuarterData?.asset_total),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="万元"
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="截至本季度末的资产总额。若您公司的资产总额每年度统计一次，同年内四个季度本指标可以填相同值" />
              </div>
            </Form.Item>
            <Form.Item label="营业收入" name="revenue_total" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="revenue_total"
                  rules={[
                    {
                      required: true,
                      message: '请输入营业收入',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(value, preQuarterData?.revenue_total),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="万元"
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="本季度的营业收入。请与您公司在驾驶舱综合指标清单中填报的数据口径保持一致，需注意本指标应为本季度新增值（综合指标清单中填报的为累计值）" />
              </div>
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item label="利润总额" required>
              <div className="flex items-center space-x-2">
                <Form.Item
                  name="profit_total"
                  rules={[
                    {
                      required: true,
                      message: '请输入利润总额',
                    },
                    {
                      warningOnly: true,
                      validator: (_, value) =>
                        commonValidate(value, preQuarterData?.profit_total),
                    },
                  ]}
                  noStyle
                >
                  <InputNumber
                    placeholder="请输入"
                    suffix="万元"
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    controls={false}
                  />
                </Form.Item>
                <FormTip title="本季度的利润总额。请与您公司在驾驶舱综合指标清单中填报的数据口径保持一致，需注意本指标应为本季度新增值（综合指标清单中填报的为累计值）" />
              </div>
            </Form.Item>
          </div>

          <div className="grid grid-cols-1">
            <Form.Item label="备注" name="remarks">
              <Input.TextArea
                className="w-full"
                placeholder="输入备注"
                disabled={type === 'VIEW'}
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
            </Form.Item>
          </div>
        </div>

        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          {type !== 'VIEW' && (
            <Button type="primary" htmlType="submit" disabled={false}>
              保存数据
            </Button>
          )}
          <Button
            disabled={false}
            onClick={() =>
              navigate({ to: '/strategic-report/development-index' })
            }
          >
            取消
          </Button>
        </div>
      </Form>
    </Spin>
  )
}
