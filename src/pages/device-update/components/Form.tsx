import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate, useParams } from '@tanstack/react-router'
import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Select,
  Spin,
  AutoComplete,
  TreeSelect,
} from 'antd'
import dayjs from 'dayjs'
import { useState, useEffect, useCallback } from 'react'

import { useApp } from '@/contexts/app.tsx'
import { useAuth } from '@/contexts/auth.tsx'
import { useCompanyOptions } from '@/hook/useCompanies'
import { type APIResponse, request } from '@/lib/request.ts'
import {
  INDUSTRY_TYPE,
  PROJECT_AREA,
  HAS_EQUIPMENT_UPGRADE,
  STANDARD_NAME,
  PROJECT_CLASS,
} from '@/universal/basic-form/constants.ts'

import type { ProjectData, SelectProjectData } from './type.ts'

const CROUP_COMPANY_ID = 'f4d1fb45-1a37-4cf7-a522-da256ab35029'
export function DeviceUpdateForm({
  type = 'CREATE',
}: Readonly<{ type?: 'CREATE' | 'UPDATE' }>) {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { id: approvalNodeId } = useParams({ strict: false })
  const { currentDate } = useApp()
  const [projectOptions, setProjectOptions] = useState<SelectProjectData[]>([])
  const [isSelectProject, setIsSelectProject] = useState<boolean>(false)
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null,
  )

  const [form] = Form.useForm()

  useEffect(() => {
    form.setFieldsValue({
      company_id: user?.company_id,
      company_name: user?.company,
      investment_year: dayjs(currentDate?.year.toString()),
    })
  }, [user?.company_id, user?.company, form, currentDate?.year])

  const { isLoading } = useQuery({
    queryKey: [
      '/industry-new/major_equipment_replacement/detail-by-approval-node-id',
      approvalNodeId,
    ],
    queryFn: async ({ queryKey: [url, id] }) => {
      const res = await request<APIResponse<ProjectData>>(url as string, {
        method: 'GET',
        params: {
          approval_node_id: id,
        },
      })

      if (res.code === 200001) {
        const {
          investment_year,
          planned_operation_time,
          actual_start_date,
          src_id,
          ...data
        } = res.data

        const formData = {
          ...data,
          investment_year: dayjs(investment_year),
          planned_operation_time: dayjs(planned_operation_time),
          actual_start_date: dayjs(actual_start_date),
        }
        // 是选择的项目就要禁用
        if (src_id) {
          setIsSelectProject(true)
        }

        form.setFieldsValue(formData)

        return formData
      }
      message.error(res.message)
    },
    enabled: !!approvalNodeId,
  })

  const onSubmit = useMutation({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mutationFn: async (values: Record<string, any>) => {
      const {
        investment_year,
        planned_operation_time,
        actual_start_date,
        ...data
      } = values
      const bodyData = {
        ...data,
        investment_year: dayjs(investment_year).format('YYYY'),
        planned_operation_time: dayjs(planned_operation_time).format(
          'YYYY-MM-DD HH:mm:ss',
        ),
        actual_start_date: dayjs(actual_start_date).format(
          'YYYY-MM-DD HH:mm:ss',
        ),
      }

      let res

      if (type === 'UPDATE') {
        res = await request<APIResponse<Record<string, string>>>(
          '/industry-new/major_equipment_replacement',
          {
            method: 'PUT',
            body: { approval_node_id: approvalNodeId, ...bodyData },
          },
        )
      } else {
        res = await request<APIResponse<Record<string, string>>>(
          '/industry-new/major_equipment_replacement',
          { method: 'POST', body: bodyData },
        )
      }

      if (res.code === 200001) {
        message.success('操作成功')
        await navigate({ to: '/strategic-report/device-update' })
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  // 项目选择
  const handleSelectProject = useCallback(
    (_: string, item: SelectProjectData) => {
      setIsSelectProject(true)
      form.setFieldsValue({
        src_id: item.project_id,
        project_name: item.project_name,
        industry_type: item.industry_type,
        project_area: item.project_area,
        project_construction_content: item.project_content,
        project_expected_outcome: item.project_expect,
        plan_investment_cur_year: item.plan_investment_cur_year,
        actual_start_date: dayjs(item.actual_start_date),
      })
    },
    [form],
  )

  // 取消禁用，清空项目
  const handleProjectChange = useCallback(() => {
    setIsSelectProject(false)
    form.setFieldsValue({
      src_id: '',
    })
  }, [form])

  // 项目搜索
  const handleSearch = useCallback(
    async (value: string) => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }

      if (!value) {
        setProjectOptions([])
        return
      }

      const timeoutId = setTimeout(async () => {
        const companyId = form.getFieldValue('company_id')
        const investmentYear = form.getFieldValue('investment_year')

        const res = await request<APIResponse<SelectProjectData[]>>(
          '/industry-new/major_equipment_replacement/find-project',
          {
            method: 'GET',
            params: {
              company_id: companyId,
              year: dayjs(investmentYear).format('YYYY'),
              project_name: value,
            },
          },
        )
        if (res.code === 200001) {
          setProjectOptions(
            res.data.map((item) => ({
              label: item.project_name,
              value: item.project_name,
              ...item,
            })),
          )
          return
        }
        message.error(res.message)
      }, 500)

      setSearchTimeout(timeoutId)
    },
    [searchTimeout, form],
  )

  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
    }
  }, [searchTimeout])

  const { options: companyOptions, isLoading: isLoadingCompanies } =
    useCompanyOptions()

  const investmentYear = Form.useWatch('investment_year', form)
  return (
    <Spin spinning={onSubmit.isPending || isLoading}>
      <Form
        form={form}
        labelCol={{ flex: '140px' }}
        labelWrap
        labelAlign="left"
        scrollToFirstError={{
          block: 'center',
          behavior: 'smooth',
        }}
        onFinish={onSubmit.mutate}
      >
        <div className="flex flex-col">
          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              label="填报年份"
              name="investment_year"
              rules={[{ required: true, message: '请选择填报年份' }]}
            >
              <DatePicker
                picker="year"
                allowClear={false}
                className="w-full"
                placeholder="请选择"
                onChange={() => {
                  form.setFieldValue('project_name', '')
                  handleProjectChange()
                }}
              />
            </Form.Item>
          </div>
          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              name="group_company_id"
              noStyle
              initialValue={CROUP_COMPANY_ID}
            ></Form.Item>
            <Form.Item
              label="集团名称"
              name="group_company_name"
              rules={[{ required: true, message: '请选择集团名称' }]}
              initialValue="中国保利集团有限公司"
            >
              <Input className="w-full" placeholder="请输入" disabled={true} />
            </Form.Item>
            <Form.Item
              label="所属行业"
              name="industry_type"
              rules={[{ required: true, message: '请选择所属行业' }]}
            >
              <Select
                allowClear
                className="w-full"
                labelRender={({ label, value }) => `${label as string}${value}`}
                onSearch={(value) =>
                  INDUSTRY_TYPE.filter((item) => item.label.includes(value))
                }
                optionFilterProp="label"
                options={INDUSTRY_TYPE}
                disabled={isSelectProject}
                placeholder="请选择所属行业"
                showSearch
              />
            </Form.Item>
          </div>
          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              label="实施企业名称"
              name="company_id"
              rules={[{ required: true, message: '请选择实施企业名称' }]}
            >
              <TreeSelect
                showSearch
                placeholder="请选择"
                allowClear={false}
                treeDefaultExpandAll
                loading={isLoadingCompanies}
                treeData={companyOptions}
                treeNodeFilterProp="name"
                fieldNames={{
                  label: 'name',
                  value: 'id',
                }}
                onChange={(_, option) => {
                  const name = option?.[0] || ''
                  form.setFieldValue('company_name', name)
                  form.setFieldValue('project_name', '')
                  handleProjectChange()
                }}
              />
            </Form.Item>
            <Form.Item name="company_name" noStyle></Form.Item>
            <Form.Item
              label="项目名称"
              name="project_name"
              rules={[{ required: true, message: '请输入项目名称' }]}
            >
              <AutoComplete
                className="w-full"
                placeholder="请输入项目名称"
                options={projectOptions}
                onSearch={handleSearch}
                onSelect={handleSelectProject}
                onChange={handleProjectChange}
              />
            </Form.Item>
            <Form.Item name="src_id" noStyle></Form.Item>
            <Form.Item name="project_id" noStyle></Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              label="项目建设地"
              name="project_area"
              rules={[{ required: true, message: '请选择项目建设地' }]}
            >
              <Select
                className="w-full"
                placeholder="请选择"
                disabled={isSelectProject}
                options={PROJECT_AREA.map((item) => ({
                  label: item,
                  value: item,
                }))}
                showSearch
              />
            </Form.Item>
            <Form.Item
              label="项目实施目的"
              name="project_implementation_purpose"
              rules={[{ required: true, message: '请选择项目实施目的' }]}
            >
              <Select
                className="w-full"
                placeholder="请选择"
                options={HAS_EQUIPMENT_UPGRADE}
              />
            </Form.Item>
          </div>
          <div className="grid grid-cols-1 gap-0">
            <Form.Item
              label="项目建设内容（对应设备）"
              rules={[
                {
                  required: true,
                  message: '请输入项目建设内容（对应设备）',
                },
              ]}
              name="project_construction_content"
            >
              <Input.TextArea
                disabled={isSelectProject}
                className="w-full"
                placeholder="请输入"
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
            </Form.Item>
          </div>
          <div className="grid grid-cols-1 gap-0">
            <Form.Item
              label="项目预期成效"
              rules={[
                {
                  required: true,
                  message: '请输入项目预期成效',
                },
              ]}
              name="project_expected_outcome"
            >
              <Input.TextArea
                disabled={isSelectProject}
                className="w-full"
                placeholder="请输入"
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              label="项目类别"
              name="project_category"
              rules={[{ required: true, message: '请选择项目类别' }]}
            >
              <Select
                className="w-full"
                placeholder="请选择"
                options={PROJECT_CLASS}
              />
            </Form.Item>
            <Form.Item
              label={`${dayjs(investmentYear).format('YYYY')}年计划投资`}
              name="plan_investment_cur_year"
              rules={[{ required: true, message: '请输入计划投资' }]}
            >
              <InputNumber
                placeholder="请输入"
                disabled={isSelectProject}
                suffix="万元"
                style={{ width: '100%' }}
                min={0}
                precision={2}
                controls={false}
              />
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              label="项目计划投产时间"
              name="planned_operation_time"
              rules={[{ required: true, message: '请选择项目计划投产时间' }]}
            >
              <DatePicker className="w-full" placeholder="请选择" />
            </Form.Item>
            <Form.Item
              label="项目开工时间"
              name="actual_start_date"
              rules={[{ required: true, message: '请选择项目开工时间' }]}
            >
              <DatePicker
                className="w-full"
                placeholder="请选择"
                disabled={isSelectProject}
              />
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              name="project_plan_total_investment"
              label="项目计划投资"
              rules={[
                {
                  required: true,
                  message: '请输入项目计划投资',
                },
              ]}
            >
              <InputNumber
                placeholder="请输入"
                suffix="万元"
                style={{ width: '100%' }}
                min={0}
                precision={2}
                controls={false}
              />
            </Form.Item>
            <Form.Item
              name="investment_completed_cur_year"
              label="实际累计完成投资"
              rules={[
                {
                  required: true,
                  message: '请输入实际累计完成投资',
                },
              ]}
            >
              <InputNumber
                placeholder="请输入"
                suffix="万元"
                style={{ width: '100%' }}
                min={0}
                precision={2}
                controls={false}
              />
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
            <Form.Item
              label="是否对应标准"
              name="has_standard"
              rules={[{ required: true, message: '请选择是否对应标准' }]}
            >
              <Select
                className="w-full"
                placeholder="请选择"
                options={[
                  {
                    label: '是',
                    value: 1,
                  },
                  {
                    label: '否',
                    value: 2,
                  },
                ]}
              />
            </Form.Item>
            <Form.Item
              label="涉及标准名称"
              name="standard_name"
              rules={[{ required: true, message: '请选择涉及标准名称' }]}
            >
              <Select
                className="w-full"
                placeholder="请选择"
                options={STANDARD_NAME}
              />
            </Form.Item>
          </div>

          <div className="grid grid-cols-1">
            <Form.Item label="备注" name="remarks">
              <Input.TextArea
                className="w-full"
                placeholder="输入备注"
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
            </Form.Item>
          </div>
        </div>

        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          <Button type="primary" htmlType="submit" disabled={false}>
            保存数据
          </Button>

          <Button
            disabled={false}
            onClick={() => navigate({ to: '/strategic-report/device-update' })}
          >
            取消
          </Button>
        </div>
      </Form>
    </Spin>
  )
}
