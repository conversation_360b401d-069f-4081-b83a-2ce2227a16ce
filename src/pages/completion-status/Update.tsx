import { useParams } from '@tanstack/react-router'

import { CompletionStatusTableUpdateMap } from './constants'

export function TableUpdate() {
  const params = useParams({ strict: false })
  const tableName =
    params.tableName as keyof typeof CompletionStatusTableUpdateMap
  const TableComponent =
    tableName && tableName in CompletionStatusTableUpdateMap
      ? CompletionStatusTableUpdateMap[tableName]
      : null

  if (!TableComponent) {
    return null
  }

  return <TableComponent />
}
