import { useQuery } from '@tanstack/react-query'
import { Card, message } from 'antd'
import dayjs from 'dayjs'
import { CalendarClockIcon, ReceiptTextIcon } from 'lucide-react'
import numeral from 'numeral'

import { useApp } from '@/contexts/app'
import { useAuth } from '@/contexts/auth'
import { type APIResponse, request } from '@/lib/request.ts'

export const SummaryCard = () => {
  const { user } = useAuth()
  const { date } = useApp()

  const getTotalPlannedInvestment = useQuery({
    queryKey: [date],
    queryFn: async ({ queryKey: [date] }) => {
      const response = await request<
        APIResponse<{ plan_total: number; completed_total: number }>
      >('/aggregate/monthly-report/count-summary', {
        query: { year: dayjs(date).format('YYYY') },
      })
      if (response.code !== 200001) {
        message.error(response.message)
        return null
      }
      return response.data
    },
    staleTime: 0,
  })
  return (
    <Card>
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">{user?.company}</h2>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度项目计划总投资：</span>
            <span className="text-xl font-semibold">
              {numeral(getTotalPlannedInvestment.data?.plan_total).format(
                '0,0.00',
              )}
              万元
            </span>
          </div>
          <div className="flex items-center gap-2">
            <ReceiptTextIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度累计已投资总额：</span>
            <span className="text-xl font-semibold">
              {numeral(getTotalPlannedInvestment.data?.completed_total).format(
                '0,0.00',
              )}
              万元
            </span>
          </div>
        </div>
      </div>
    </Card>
  )
}
