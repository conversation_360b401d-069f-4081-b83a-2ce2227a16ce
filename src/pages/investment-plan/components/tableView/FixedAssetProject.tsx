import { useQuery } from '@tanstack/react-query'
import { usePara<PERSON>, useSearch, useRouter } from '@tanstack/react-router'
import {
  Card,
  Button,
  Table,
  Typography,
  type TableColumnsType,
  type TableProps,
} from 'antd'
import { createStyles } from 'antd-style'
import { useMemo, useCallback, useEffect, useState } from 'react'

import { type APIResponse, request } from '@/lib/request.ts'
import {
  INDUSTRY_TYPE,
  INVESTOR_TYPE_OPTIONS,
  PROJECT_CATEGORY,
} from '@/universal/basic-form/constants.ts'

import { InvestmentPlanTableNamesMap } from '../../constants.ts'

import type { FixedAssetProject } from './types.ts'

const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customTable: css`
      ${antCls}-table-thead > tr > th {
        background-color: #e5ebfe;
        font-weight: 400;
      }
    `,
  }
})

export const FixedAssetProjectPlanView = () => {
  const { styles } = useStyle()
  const { tableName } = useParams({ strict: false })
  const summaryData = useSearch({
    strict: false,
  }) as {
    id: string
    year: string
    company_id: string
    company_name: string
    consolidation: string
  }

  const [filters, setFilters] = useState({
    page_num: 1,
    page_size: 10,
    use_total: 1,
    belong_company_id: '',
    year: '',
    consolidation: '',
  })

  useEffect(() => {
    if (summaryData?.year) {
      setFilters((prev) => ({
        ...prev,
        year: summaryData.year,
        belong_company_id: summaryData.company_id,
        consolidation: summaryData.consolidation,
      }))
    }
  }, [
    setFilters,
    summaryData?.year,
    summaryData?.company_id,
    summaryData?.consolidation,
  ])

  // 获取初始数据
  const { data, isLoading } = useQuery({
    queryKey: ['/fixed-assets/list', filters] as const,
    queryFn: async ({ queryKey: [url, filters] }) => {
      const response = await request<
        APIResponse<{
          Total: number
          Data: FixedAssetProject[]
        }>
      >(url as string, {
        query: {
          ...filters,
        },
      })
      if (response.code !== 200001) return null
      return response?.data ?? null
    },
    staleTime: 0,
    retry: false,
    enabled: !!filters?.year && !!filters?.belong_company_id,
  })

  // 表格列配置
  const columns: TableColumnsType<FixedAssetProject> = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '项目名称',
        dataIndex: 'project_name',
        minWidth: 160,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
      {
        title: '投资分类',
        dataIndex: 'investor_type',
        width: 100,
        render: (value) => {
          return INVESTOR_TYPE_OPTIONS.find((item) => item.value === value)
            ?.label
        },
      },
      {
        title: '项目地点',
        dataIndex: 'project_area',
        width: 150,
      },
      {
        title: '项目分类',
        dataIndex: 'project_category',
        render: (value) => {
          return value
            ?.split(',')
            .map(
              (item: string) =>
                PROJECT_CATEGORY.find((i) => i.value === item)?.label + ',',
            )
        },
      },
      {
        title: '项目内容',
        dataIndex: 'project_content',
        minWidth: 160,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
      {
        title: '所属行业',
        dataIndex: 'industry_type',
        minWidth: 100,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => {
          return (
            INDUSTRY_TYPE.find((item) => item.value === value)?.label + value
          )
        },
      },
    ]
  }, [])

  const handleTableChange = useCallback<
    NonNullable<TableProps<FixedAssetProject>['onChange']>
  >(
    (pagination) => {
      setFilters((prev) => ({
        ...prev,
        page_num: pagination.current || 1,
        page_size: pagination.pageSize || prev.page_size,
      }))
    },
    [setFilters],
  )

  const title =
    InvestmentPlanTableNamesMap[
      tableName as keyof typeof InvestmentPlanTableNamesMap
    ]
  const router = useRouter()

  return (
    <div className="flex h-full flex-col">
      <Card title={title}>
        <div className="mb-4 flex items-center justify-between">
          <p>填报年份：{summaryData?.year}</p>
          <p>编制单位：{summaryData?.company_name}</p>
          <p>金额：万元</p>
        </div>
        <Table
          size="small"
          tableLayout="auto"
          bordered
          className={styles.customTable}
          dataSource={data?.Data ?? []}
          loading={isLoading}
          columns={columns}
          scroll={{ x: 'max-content' }}
          sticky={{ offsetHeader: 48 }}
          pagination={{
            showQuickJumper: true,
            showSizeChanger: true,
            total: data?.Total,
          }}
          rowKey="approval_id"
          onChange={handleTableChange}
        />
        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          <Button onClick={() => router.history.back()}>返回</Button>
        </div>
      </Card>
    </div>
  )
}
