import { useQuery } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'
import {
  Button,
  Card,
  Form,
  Table,
  Tabs,
  DatePicker,
  Divider,
  Tag,
  type TableColumnsType,
  type TableProps,
  type TabsProps,
  Typography,
} from 'antd'
import { createStyles } from 'antd-style'
import dayjs from 'dayjs'
import { CalendarClockIcon, FilterIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo, useCallback, useEffect, useState } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { useApp } from '@/contexts/app'
import { useAuth } from '@/contexts/auth.tsx'
import { exportExcel } from '@/lib/file'
import { type APIResponse, request } from '@/lib/request'
import { convertSortOrder } from '@/lib/sortUtils'
import {
  COMPANY_LEVEL,
  INVESTMENT_STATUS,
  INVESTMENT_STATUS_MAP,
} from '@/universal/data-summary/constants.ts'
import { RejectModal } from '@/universal/Reject'
import { ReportModal } from '@/universal/Report'

import type { InvestmentPlanPageDTO } from './types'

interface SearchFormValues {
  year: dayjs.Dayjs // 年份
  company_id?: string // 填报单位
}

// 修改tabs样式
const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customCard: css`
      ${antCls}-card-body {
        padding-top: 0;
      }
    `,
  }
})

export function InvestmentPlan() {
  const { styles } = useStyle()
  const { company } = useAuth()
  const { currentDate } = useApp()
  const [form] = Form.useForm()
  const [open, setOpen] = useState(false)
  const [selectKey, setSelectKey] = useState<string[]>([])
  const [rejectOpen, setRejectOpen] = useState(false)
  const [rejectSelectKey, setRejectSelectKey] = useState<string[]>([])
  const [filters, setFilters] = useState({
    page_num: 1,
    page_size: 10,
    use_total: 1,
    company_id: '',
    year: '',
    consolidation: 0,
    sort_field: '',
    sort_order: '',
  })

  useEffect(() => {
    if (company?.level && currentDate?.year) {
      const initialConsolidation =
        company?.level === COMPANY_LEVEL.THIRD ? 2 : 1
      setFilters((prev) => ({
        ...prev,
        year: currentDate.year,
        consolidation: initialConsolidation,
      }))
      form.setFieldsValue({
        year: dayjs(currentDate.year.toString(), 'YYYY'),
      })
    }
  }, [company?.level, setFilters, currentDate?.year, form])

  const isStatisticalQueryEnabled = useMemo(
    () => !!company?.id && !!filters.consolidation && !!currentDate?.year,
    [company?.id, filters.consolidation, currentDate?.year],
  )

  const isListQueryEnabled = useMemo(
    () => !!filters.consolidation,
    [filters.consolidation],
  )

  const { data: statisticalData } = useQuery({
    queryKey: [
      '/aggregate/plan/summary',
      company?.id,
      filters.consolidation,
      currentDate?.year,
    ] as const,
    queryFn: async ({ queryKey: [url, company_id, consolidation, date] }) => {
      const response = await request<APIResponse<InvestmentPlanPageDTO>>(
        url as string,
        {
          query: {
            company_id,
            consolidation,
            year: date,
          },
        },
      )
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: isStatisticalQueryEnabled,
  })

  const { data, isLoading, isFetching, refetch } = useQuery({
    queryKey: ['/aggregate/plan/list', filters] as const,
    queryFn: async ({ queryKey: [url, filters] }) => {
      const response = await request<
        APIResponse<{
          Data: InvestmentPlanPageDTO[]
          Total: number
        }>
      >(url as string, {
        query: {
          ...filters,
          sort_order: convertSortOrder(filters.sort_order),
        },
      })

      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: isListQueryEnabled,
  })

  /** 导出数据 */
  const handleExportData = useCallback(async (id: string) => {
    await exportExcel('/plan-summary/investment-export-excel-data', { id })
  }, [])

  const columns: TableColumnsType<InvestmentPlanPageDTO> = useMemo(() => {
    const getSortOrder = (field: string): 'ascend' | 'descend' | undefined => {
      if (filters.sort_field === field) {
        if (
          filters.sort_order === 'ascend' ||
          filters.sort_order === 'descend'
        ) {
          return filters.sort_order
        }
      }
      return undefined
    }

    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '汇总单位',
        dataIndex: 'company_name',
        width: 160,
        ellipsis: {
          showTitle: false,
        },
        render: (value, record) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            <Link
              to="/data-summary/investment-plan/table"
              search={{
                id: record.id,
                year: record.investment_year,
                company_name: record.company_name,
                company_id: record.company_id,
                consolidation: record.consolidation,
              }}
            >
              <Button type="link" size="small">
                {value}
              </Button>
            </Link>
          </Typography.Text>
        ),
      },
      {
        title: '填报年份',
        dataIndex: 'investment_year',
        width: 100,
      },
      {
        title: '本年度计划投资（万元）',
        dataIndex: 'current_year_total',
        width: 200,
        align: 'right',
        sorter: true,
        sortOrder: getSortOrder('current_year_total'),
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },
      {
        title: '固资计划额（万元）',
        dataIndex: 'fixed_assets_amount',
        width: 180,
        align: 'right',
        sorter: true,
        sortOrder: getSortOrder('fixed_assets_amount'),
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },
      {
        title: '股权计划额（万元）',
        dataIndex: 'equity_investment_amount',
        width: 180,
        align: 'right',
        sorter: true,
        sortOrder: getSortOrder('equity_investment_amount'),
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },
      ...(company?.level === COMPANY_LEVEL.GROUP
        ? [
            {
              title: '确认状态',
              dataIndex: 'status',
              width: 100,
              render: (value: number) => {
                if (value === 1) {
                  return <Tag color="green">已确认</Tag>
                } else if (value === 2) {
                  return <Tag color="default">未确认</Tag>
                }
              },
            },
          ]
        : [
            {
              title: '状态',
              dataIndex: 'approval_node_status',
              width: 100,
              render: (value: keyof typeof INVESTMENT_STATUS_MAP) => (
                <Tag
                  color={INVESTMENT_STATUS_MAP[value]?.type}
                  className="!text-[12px]"
                >
                  {INVESTMENT_STATUS_MAP[value]?.label}
                </Tag>
              ),
            },
          ]),

      {
        title: '上报人',
        dataIndex: 'op_name',
        width: 120,
      },
      {
        title: '上报时间',
        dataIndex: 'approval_update_at',
        width: 180,
      },
      {
        title: '操作',
        width: 150,
        fixed: 'right',
        render: (_, record) => {
          const ReportButton = () => (
            <Button
              type="link"
              size="small"
              disabled={record.status === INVESTMENT_STATUS.REPORTED}
              onClick={() => {
                setOpen(true)
                setSelectKey([record.approval_node_id])
              }}
            >
              上报
            </Button>
          )
          const RejectButton = () => (
            <Button
              type="link"
              size="small"
              onClick={() => {
                setRejectOpen(true)
                setRejectSelectKey([record.approval_node_id])
              }}
              disabled={!record?.reject_view}
            >
              驳回
            </Button>
          )
          const ConfirmButton = () => (
            <Button type="link" size="small">
              数据确认
            </Button>
          )
          // 集团汇总
          const renderButton = () => {
            if (company?.level === COMPANY_LEVEL.GROUP) {
              return record.consolidation === 1 ? (
                <ConfirmButton />
              ) : (
                <RejectButton />
              )
            } else if (company?.level === COMPANY_LEVEL.SECOND) {
              return record.consolidation === 1 ? (
                <ReportButton />
              ) : (
                <RejectButton />
              )
            } else {
              return <ReportButton />
            }
          }

          return (
            <>
              {renderButton()}
              <Divider type="vertical" />
              <Button
                type="link"
                size="small"
                onClick={() => handleExportData(record.id)}
              >
                导出
              </Button>
            </>
          )
        },
      },
    ]
  }, [filters.sort_field, filters.sort_order, handleExportData, company?.level])

  const handleTableChange = useCallback<
    NonNullable<TableProps<InvestmentPlanPageDTO>['onChange']>
  >(
    (pagination, _filters, sorter) => {
      let sort_field = ''
      let sort_order = ''
      if (Array.isArray(sorter)) {
        if (sorter.length > 0) {
          sort_field = (sorter[0]?.field as string) || ''
          sort_order = sorter[0]?.order || ''
        }
      } else if (sorter) {
        sort_field = (sorter.field as string) || ''
        sort_order = sorter.order || ''
      }
      setFilters((prev) => ({
        ...prev,
        sort_field,
        sort_order,
        page_num: pagination.current || 1,
        page_size: pagination.pageSize || prev.page_size,
      }))
    },
    [setFilters],
  )

  const onSearch = useCallback(
    (values: SearchFormValues) => {
      setFilters((prev) => ({
        ...prev,
        page_num: 1,
        company_id: values.company_id || '',
        year: dayjs(values.year).format('YYYY'),
      }))
    },
    [setFilters],
  )

  const onReset = useCallback(() => {
    form.resetFields()
    setFilters((prev) => ({
      ...prev,
      page_num: 1,
      company_id: '',
      sort_field: '',
      sort_order: '',
      year: '',
    }))
  }, [form, setFilters])

  const tabs: TabsProps['items'] = [
    {
      key: '1',
      label: '汇总数据',
    },
    {
      key: '2',
      label: '所有数据',
    },
  ]

  return (
    <div className="flex h-full flex-col gap-4">
      <Card className={styles.customCard}>
        {company?.level !== COMPANY_LEVEL.THIRD && (
          <Tabs
            activeKey={`${filters.consolidation}`}
            items={tabs}
            onChange={(key) => {
              setFilters((prev) => ({
                ...prev,
                page_num: 1,
                company_id: '',
                sort_field: '',
                sort_order: '',
                year: '',
                consolidation: +key,
              }))
              form.resetFields()
            }}
          />
        )}

        <div className="space-y-6">
          <h2 className="mt-3 text-xl font-semibold">{company?.name}</h2>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度计划投资总额：</span>
            <span className="text-xl font-semibold">
              {numeral(statisticalData?.current_year_total).format('0,0.00')}
              万元
            </span>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-col gap-4">
          <Form form={form} onFinish={onSearch} onReset={onReset}>
            <div className="flex items-end gap-2">
              <div className="grid flex-1 grid-cols-3 gap-4 2xl:grid-cols-4">
                <Form.Item className="!mb-0" name="year">
                  <DatePicker
                    className="w-full"
                    picker="year"
                    format="YYYY"
                    prefix={<FormItemPrefix title="填报年份" />}
                  />
                </Form.Item>
              </div>
              <div className="flex shrink-0 grow-0 items-center gap-2">
                <Button
                  type="default"
                  icon={<FilterIcon className="size-3.5" />}
                />
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
                <Button type="text" htmlType="reset">
                  清空
                </Button>
              </div>
            </div>
          </Form>
          <div className="flex items-center gap-2 text-[16px] font-[600]">
            投资计划
          </div>
          <SkeletonTable
            loading={isLoading || isFetching}
            columns={columns as SkeletonTableColumnsType[]}
          >
            <Table
              size="small"
              dataSource={data?.Data ?? []}
              columns={columns}
              scroll={{ x: 'max-content' }}
              sticky={{ offsetHeader: 48 }}
              pagination={{
                showQuickJumper: true,
                showSizeChanger: true,
                total: data?.Total,
              }}
              rowKey="id"
              onChange={handleTableChange}
            />
          </SkeletonTable>
        </div>
      </Card>
      <ReportModal
        open={open}
        onClose={() => setOpen(false)}
        apiUrl={'/aggregate/plan/pending-mul'}
        apiParams={{ node_ids: selectKey }}
        onSuccess={refetch}
      />
      <RejectModal
        open={rejectOpen}
        setOpen={() => setRejectOpen(false)}
        rejectUrl={'/aggregate/plan/reject-mul'}
        rejectKey={rejectSelectKey}
      />
    </div>
  )
}
